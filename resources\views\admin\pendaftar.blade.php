@extends('layouts.admin')

@section('title', 'Data Pendaftar')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users me-2"></i>Data Pendaftar
        </h1>
        <div class="text-muted">
            Total: {{ $pendaftars->total() }} pendaftar
        </div>
    </div>

    <!-- Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>Filter & Pencarian
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.pendaftar') }}">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Pencarian</label>
                        <input type="text" 
                               class="form-control" 
                               id="search" 
                               name="search" 
                               value="{{ request('search') }}" 
                               placeholder="Nama, No KK, Telepon, Pekerjaan...">
                    </div>
                    <div class="col-md-3">
                        <label for="status_pernikahan" class="form-label">Status Pernikahan</label>
                        <select class="form-select" id="status_pernikahan" name="status_pernikahan">
                            <option value="">Semua Status</option>
                            <option value="belum_menikah" {{ request('status_pernikahan') == 'belum_menikah' ? 'selected' : '' }}>
                                Belum Menikah
                            </option>
                            <option value="menikah" {{ request('status_pernikahan') == 'menikah' ? 'selected' : '' }}>
                                Menikah
                            </option>
                            <option value="cerai_hidup" {{ request('status_pernikahan') == 'cerai_hidup' ? 'selected' : '' }}>
                                Cerai Hidup
                            </option>
                            <option value="cerai_mati" {{ request('status_pernikahan') == 'cerai_mati' ? 'selected' : '' }}>
                                Cerai Mati
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="mempunyai_rumah" class="form-label">Kepemilikan Rumah</label>
                        <select class="form-select" id="mempunyai_rumah" name="mempunyai_rumah">
                            <option value="">Semua</option>
                            <option value="ya" {{ request('mempunyai_rumah') == 'ya' ? 'selected' : '' }}>
                                Sudah Punya Rumah
                            </option>
                            <option value="tidak" {{ request('mempunyai_rumah') == 'tidak' ? 'selected' : '' }}>
                                Belum Punya Rumah
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="{{ route('admin.pendaftar') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table me-2"></i>Daftar Pendaftar
            </h6>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle btn-sm" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-1"></i>Export
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                </ul>
            </div>
        </div>
        <div class="card-body">
            @if($pendaftars->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover" id="dataTable">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama Lengkap</th>
                                <th>No. KK</th>
                                <th>Telepon</th>
                                <th>Pekerjaan</th>
                                <th>Status Nikah</th>
                                <th>Jml Keluarga</th>
                                <th>Penghasilan</th>
                                <th>Punya Rumah</th>
                                <th>Tanggal Daftar</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($pendaftars as $index => $pendaftar)
                                <tr>
                                    <td>{{ $pendaftars->firstItem() + $index }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <div class="font-weight-bold">{{ $pendaftar->nama_lengkap }}</div>
                                                <small class="text-muted">ID: {{ $pendaftar->id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $pendaftar->no_kk }}</td>
                                    <td>
                                        <a href="tel:{{ $pendaftar->no_telepon }}" class="text-decoration-none">
                                            {{ $pendaftar->no_telepon }}
                                        </a>
                                    </td>
                                    <td>{{ $pendaftar->pekerjaan }}</td>
                                    <td>
                                        <span class="badge bg-{{ $pendaftar->status_pernikahan == 'menikah' ? 'success' : 'secondary' }}">
                                            {{ ucwords(str_replace('_', ' ', $pendaftar->status_pernikahan)) }}
                                        </span>
                                    </td>
                                    <td>{{ $pendaftar->jumlah_keluarga }} orang</td>
                                    <td>Rp {{ number_format($pendaftar->jumlah_penghasilan, 0, ',', '.') }}</td>
                                    <td>
                                        <span class="badge bg-{{ $pendaftar->mempunyai_rumah == 'ya' ? 'success' : 'warning' }}">
                                            {{ $pendaftar->mempunyai_rumah == 'ya' ? 'Ya' : 'Tidak' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ $pendaftar->created_at->format('d/m/Y') }}</small><br>
                                        <small class="text-muted">{{ $pendaftar->created_at->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.show', $pendaftar->id) }}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.edit', $pendaftar->id) }}" 
                                               class="btn btn-sm btn-outline-warning" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Hapus"
                                                    onclick="confirmDelete({{ $pendaftar->id }}, '{{ $pendaftar->nama_lengkap }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Menampilkan {{ $pendaftars->firstItem() }} - {{ $pendaftars->lastItem() }} 
                        dari {{ $pendaftars->total() }} data
                    </div>
                    <div>
                        {{ $pendaftars->appends(request()->query())->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-muted">Tidak ada data pendaftar</h5>
                    <p class="text-muted">
                        @if(request()->hasAny(['search', 'status_pernikahan', 'mempunyai_rumah']))
                            Tidak ada data yang sesuai dengan filter yang dipilih.
                        @else
                            Belum ada pendaftar yang terdaftar.
                        @endif
                    </p>
                    @if(request()->hasAny(['search', 'status_pernikahan', 'mempunyai_rumah']))
                        <a href="{{ route('admin.pendaftar') }}" class="btn btn-primary">
                            <i class="fas fa-times me-1"></i>Reset Filter
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Konfirmasi Hapus
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus data pendaftar <strong id="deleteName"></strong>?</p>
                <p class="text-muted small">Tindakan ini tidak dapat dibatalkan.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Hapus
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
    
    .table th {
        font-size: 0.85rem;
        white-space: nowrap;
    }
    
    .table td {
        font-size: 0.9rem;
        vertical-align: middle;
    }
    
    .btn-group .btn {
        margin-right: 2px;
    }
    
    .badge {
        font-size: 0.75rem;
    }
</style>
@endpush

@push('scripts')
<script>
    function confirmDelete(id, name) {
        document.getElementById('deleteName').textContent = name;
        document.getElementById('deleteForm').action = '/admin/pendaftar/' + id;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // Initialize DataTable for better UX (optional)
    $(document).ready(function() {
        // You can add DataTable initialization here if needed
        // $('#dataTable').DataTable({
        //     "paging": false,
        //     "searching": false,
        //     "info": false
        // });
    });
</script>
@endpush
