<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pendaftars', function (Blueprint $table) {
            $table->id();
            $table->string('nama_lengkap');
            $table->string('no_kk');
            $table->text('alamat_ktp');
            $table->text('alamat_domisili');
            $table->string('no_telepon');
            $table->string('pekerjaan');
            $table->enum('status_pernikahan', ['belum_menikah', 'menikah', 'cerai_hidup', 'cerai_mati']);
            $table->integer('jumlah_keluarga');
            $table->decimal('jumlah_penghasilan', 15, 2);
            $table->enum('mempunyai_rumah', ['ya', 'tidak']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pendaftars');
    }
};
