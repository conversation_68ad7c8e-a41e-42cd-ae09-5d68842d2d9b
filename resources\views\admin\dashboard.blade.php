@extends('layouts.admin')

@section('title', 'Dashboard Admin')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </h1>
        <div class="text-muted">
            <i class="fas fa-calendar me-1"></i>
            {{ now()->format('d F Y') }}
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Pendaftar
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalPendaftar }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Pendaftar Hari Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ \App\Models\Pendaftar::whereDate('created_at', today())->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Pendaftar Minggu Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ \App\Models\Pendaftar::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pendaftar Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ \App\Models\Pendaftar::whereMonth('created_at', now()->month)->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Biodata Pendaftar -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>Biodata Pendaftar Terdaftar
                    </h6>
                    <div class="btn-group">
                        <a href="{{ route('admin.biodata') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-address-card me-1"></i>Semua Biodata
                        </a>
                        <a href="{{ route('admin.pendaftar') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-table me-1"></i>Lihat Tabel
                        </a>
                        <button class="btn btn-success btn-sm" onclick="exportData()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if($pendaftarTerbaru->count() > 0)
                        <div class="row">
                            @foreach($pendaftarTerbaru as $pendaftar)
                                <div class="col-lg-6 col-xl-4 mb-4">
                                    <div class="card border-left-primary h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="avatar bg-primary rounded-circle d-flex align-items-center justify-content-center me-3"
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-user text-white fa-lg"></i>
                                                </div>
                                                <div>
                                                    <h6 class="font-weight-bold text-primary mb-0">{{ $pendaftar->nama_lengkap }}</h6>
                                                    <small class="text-muted">ID: {{ $pendaftar->id }}</small>
                                                </div>
                                            </div>

                                            <div class="biodata-info">
                                                <div class="row mb-2">
                                                    <div class="col-5">
                                                        <small class="text-muted">No. KK:</small>
                                                    </div>
                                                    <div class="col-7">
                                                        <small class="font-weight-bold">{{ $pendaftar->no_kk }}</small>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-5">
                                                        <small class="text-muted">Telepon:</small>
                                                    </div>
                                                    <div class="col-7">
                                                        <small class="font-weight-bold">
                                                            <a href="tel:{{ $pendaftar->no_telepon }}" class="text-decoration-none">
                                                                {{ $pendaftar->no_telepon }}
                                                            </a>
                                                        </small>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-5">
                                                        <small class="text-muted">Pekerjaan:</small>
                                                    </div>
                                                    <div class="col-7">
                                                        <small class="font-weight-bold">{{ $pendaftar->pekerjaan }}</small>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-5">
                                                        <small class="text-muted">Status:</small>
                                                    </div>
                                                    <div class="col-7">
                                                        <span class="badge bg-{{ $pendaftar->status_pernikahan == 'menikah' ? 'success' : 'secondary' }} badge-sm">
                                                            {{ ucwords(str_replace('_', ' ', $pendaftar->status_pernikahan)) }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-5">
                                                        <small class="text-muted">Jml Keluarga:</small>
                                                    </div>
                                                    <div class="col-7">
                                                        <small class="font-weight-bold">{{ $pendaftar->jumlah_keluarga }} orang</small>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-5">
                                                        <small class="text-muted">Penghasilan:</small>
                                                    </div>
                                                    <div class="col-7">
                                                        <small class="font-weight-bold text-success">
                                                            Rp {{ number_format($pendaftar->jumlah_penghasilan, 0, ',', '.') }}
                                                        </small>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-5">
                                                        <small class="text-muted">Punya Rumah:</small>
                                                    </div>
                                                    <div class="col-7">
                                                        <span class="badge bg-{{ $pendaftar->mempunyai_rumah == 'ya' ? 'success' : 'warning' }} badge-sm">
                                                            {{ $pendaftar->mempunyai_rumah == 'ya' ? 'Ya' : 'Tidak' }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-5">
                                                        <small class="text-muted">Alamat:</small>
                                                    </div>
                                                    <div class="col-7">
                                                        <small class="font-weight-bold">
                                                            {{ Str::limit($pendaftar->alamat_domisili, 50) }}
                                                        </small>
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-12">
                                                        <small class="text-muted">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            Daftar: {{ $pendaftar->created_at->format('d/m/Y H:i') }}
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-flex justify-content-between">
                                                <a href="{{ route('admin.show', $pendaftar->id) }}"
                                                   class="btn btn-outline-info btn-sm">
                                                    <i class="fas fa-eye me-1"></i>Detail
                                                </a>
                                                <a href="{{ route('admin.edit', $pendaftar->id) }}"
                                                   class="btn btn-outline-warning btn-sm">
                                                    <i class="fas fa-edit me-1"></i>Edit
                                                </a>
                                                <a href="tel:{{ $pendaftar->no_telepon }}"
                                                   class="btn btn-outline-success btn-sm">
                                                    <i class="fas fa-phone me-1"></i>Call
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        @if(\App\Models\Pendaftar::count() > 5)
                            <div class="text-center mt-3">
                                <a href="{{ route('admin.pendaftar') }}" class="btn btn-primary">
                                    <i class="fas fa-users me-2"></i>Lihat Semua Pendaftar ({{ \App\Models\Pendaftar::count() }} orang)
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-muted">Belum Ada Pendaftar</h5>
                            <p class="text-muted">Belum ada yang mendaftar sebagai penghuni rusun.</p>
                            <a href="{{ route('pendaftar.create') }}" class="btn btn-primary" target="_blank">
                                <i class="fas fa-plus me-2"></i>Buka Form Pendaftaran
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-lg-8">

        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>Aksi Cepat
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.biodata') }}" class="btn btn-info">
                            <i class="fas fa-address-card me-2"></i>Biodata Lengkap
                        </a>
                        <a href="{{ route('admin.pendaftar') }}" class="btn btn-primary">
                            <i class="fas fa-table me-2"></i>Tabel Data
                        </a>
                        <a href="{{ route('pendaftar.create') }}" class="btn btn-success" target="_blank">
                            <i class="fas fa-plus me-2"></i>Form Pendaftaran
                        </a>
                        <a href="{{ route('home') }}" class="btn btn-secondary" target="_blank">
                            <i class="fas fa-home me-2"></i>Lihat Website
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>Ringkasan Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="text-sm">Sudah Punya Rumah</span>
                            <span class="text-sm font-weight-bold">
                                {{ \App\Models\Pendaftar::where('mempunyai_rumah', 'ya')->count() }}
                            </span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" 
                                 style="width: {{ $totalPendaftar > 0 ? (\App\Models\Pendaftar::where('mempunyai_rumah', 'ya')->count() / $totalPendaftar) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="text-sm">Belum Punya Rumah</span>
                            <span class="text-sm font-weight-bold">
                                {{ \App\Models\Pendaftar::where('mempunyai_rumah', 'tidak')->count() }}
                            </span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-warning" 
                                 style="width: {{ $totalPendaftar > 0 ? (\App\Models\Pendaftar::where('mempunyai_rumah', 'tidak')->count() / $totalPendaftar) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="text-sm">Sudah Menikah</span>
                            <span class="text-sm font-weight-bold">
                                {{ \App\Models\Pendaftar::where('status_pernikahan', 'menikah')->count() }}
                            </span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-info" 
                                 style="width: {{ $totalPendaftar > 0 ? (\App\Models\Pendaftar::where('status_pernikahan', 'menikah')->count() / $totalPendaftar) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
    .avatar {
        width: 3rem;
        height: 3rem;
    }
    .text-xs {
        font-size: 0.7rem;
    }
    .biodata-info {
        font-size: 0.85rem;
    }
    .biodata-info .row {
        margin-bottom: 0.25rem;
    }
    .badge-sm {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    .card.border-left-primary {
        transition: transform 0.2s ease-in-out;
    }
    .card.border-left-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .btn-group .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
</style>
@endsection

@push('scripts')
<script>
    function exportData() {
        // Simulasi export - bisa diimplementasikan dengan library seperti jsPDF atau Excel.js
        alert('Fitur export akan segera tersedia!\n\nAnda dapat menggunakan fitur "Lihat Tabel" untuk melihat data dalam format tabel yang dapat di-copy.');

        // Alternatif: redirect ke halaman pendaftar dengan parameter export
        // window.open('{{ route("admin.pendaftar") }}?export=true', '_blank');
    }

    // Auto refresh statistik setiap 30 detik
    setInterval(function() {
        // Refresh halaman setiap 5 menit untuk update data terbaru
        // location.reload();
    }, 300000); // 5 menit

    // Tooltip untuk informasi tambahan
    $(document).ready(function() {
        $('[data-bs-toggle="tooltip"]').tooltip();

        // Animasi counter untuk statistik
        $('.h5').each(function() {
            var $this = $(this);
            var countTo = parseInt($this.text());

            if (!isNaN(countTo)) {
                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'linear',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(this.countNum);
                    }
                });
            }
        });
    });
</script>
@endpush
