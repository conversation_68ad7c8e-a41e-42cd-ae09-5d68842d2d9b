<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Pendaftar;

class AdminController extends Controller
{
    // Middleware akan ditangani di routes

    public function dashboard()
    {
        $totalPendaftar = Pendaftar::count();
        $pendaftarTerbaru = Pendaftar::latest()->take(5)->get();

        return view('admin.dashboard', compact('totalPendaftar', 'pendaftarTerbaru'));
    }

    public function pendaftar(Request $request)
    {
        $query = Pendaftar::query();

        // Filter berdasarkan pencarian
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('no_kk', 'like', "%{$search}%")
                  ->orWhere('no_telepon', 'like', "%{$search}%")
                  ->orWhere('pekerjaan', 'like', "%{$search}%");
            });
        }

        // Filter berdasarkan status pernikahan
        if ($request->has('status_pernikahan') && $request->status_pernikahan) {
            $query->where('status_pernikahan', $request->status_pernikahan);
        }

        // Filter berdasarkan kepemilikan rumah
        if ($request->has('mempunyai_rumah') && $request->mempunyai_rumah) {
            $query->where('mempunyai_rumah', $request->mempunyai_rumah);
        }

        $pendaftars = $query->orderBy('created_at', 'desc')->paginate(10);

        return view('admin.pendaftar', compact('pendaftars'));
    }

    public function show($id)
    {
        $pendaftar = Pendaftar::findOrFail($id);
        return view('admin.show', compact('pendaftar'));
    }

    public function edit($id)
    {
        $pendaftar = Pendaftar::findOrFail($id);
        return view('admin.edit', compact('pendaftar'));
    }

    public function update(Request $request, $id)
    {
        $pendaftar = Pendaftar::findOrFail($id);

        $validated = $request->validate([
            'nama_lengkap' => 'required|string|max:255',
            'no_kk' => 'required|string|max:255',
            'alamat_ktp' => 'required|string',
            'alamat_domisili' => 'required|string',
            'no_telepon' => 'required|string|max:255',
            'pekerjaan' => 'required|string|max:255',
            'status_pernikahan' => 'required|in:belum_menikah,menikah,cerai_hidup,cerai_mati',
            'jumlah_keluarga' => 'required|integer|min:1',
            'jumlah_penghasilan' => 'required|numeric|min:0',
            'mempunyai_rumah' => 'required|in:ya,tidak'
        ]);

        $pendaftar->update($validated);

        return redirect()->route('admin.pendaftar')
            ->with('success', 'Data pendaftar berhasil diperbarui.');
    }

    public function destroy($id)
    {
        $pendaftar = Pendaftar::findOrFail($id);
        $pendaftar->delete();

        return redirect()->route('admin.pendaftar')
            ->with('success', 'Data pendaftar berhasil dihapus.');
    }
}
