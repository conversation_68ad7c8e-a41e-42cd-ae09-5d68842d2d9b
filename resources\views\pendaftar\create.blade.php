@extends('layouts.app')

@section('title', 'Formulir Pendaftaran')

@push('styles')
<style>
    .form-section {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
        padding: 40px 0;
    }
    
    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .form-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .form-body {
        padding: 40px;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    
    .btn-submit {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
        border: none;
        padding: 15px 40px;
        font-size: 18px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
    }
    
    .alert {
        border-radius: 8px;
        border: none;
    }
    
    .required {
        color: #dc2626;
    }
</style>
@endpush

@section('content')
<div class="form-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card">
                    <div class="form-header">
                        <h1 class="h2 mb-2">
                            <i class="fas fa-edit me-3"></i>
                            Formulir Pendaftaran Penghuni Rusun
                        </h1>
                        <p class="mb-0">Lengkapi data diri Anda dengan benar dan lengkap</p>
                    </div>
                    
                    <div class="form-body">
                        @if(session('success'))
                            <div class="alert alert-success d-flex align-items-center mb-4">
                                <i class="fas fa-check-circle me-3"></i>
                                <div>
                                    <strong>Berhasil!</strong> {{ session('success') }}
                                </div>
                            </div>
                        @endif
                        
                        @if($errors->any())
                            <div class="alert alert-danger mb-4">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Terdapat kesalahan:</h6>
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        <form action="{{ route('pendaftar.store') }}" method="POST">
                            @csrf
                            
                            <!-- Data Pribadi -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-user me-2"></i>Data Pribadi
                                    </h5>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="nama_lengkap" class="form-label">
                                            Nama Lengkap <span class="required">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control @error('nama_lengkap') is-invalid @enderror" 
                                               id="nama_lengkap" 
                                               name="nama_lengkap" 
                                               value="{{ old('nama_lengkap') }}" 
                                               required>
                                        @error('nama_lengkap')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="no_kk" class="form-label">
                                            Nomor Kartu Keluarga <span class="required">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control @error('no_kk') is-invalid @enderror" 
                                               id="no_kk" 
                                               name="no_kk" 
                                               value="{{ old('no_kk') }}" 
                                               required>
                                        @error('no_kk')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="no_telepon" class="form-label">
                                            Nomor Telepon <span class="required">*</span>
                                        </label>
                                        <input type="tel" 
                                               class="form-control @error('no_telepon') is-invalid @enderror" 
                                               id="no_telepon" 
                                               name="no_telepon" 
                                               value="{{ old('no_telepon') }}" 
                                               required>
                                        @error('no_telepon')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="pekerjaan" class="form-label">
                                            Pekerjaan <span class="required">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control @error('pekerjaan') is-invalid @enderror" 
                                               id="pekerjaan" 
                                               name="pekerjaan" 
                                               value="{{ old('pekerjaan') }}" 
                                               required>
                                        @error('pekerjaan')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Alamat -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-map-marker-alt me-2"></i>Alamat
                                    </h5>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="alamat_ktp" class="form-label">
                                            Alamat KTP <span class="required">*</span>
                                        </label>
                                        <textarea class="form-control @error('alamat_ktp') is-invalid @enderror" 
                                                  id="alamat_ktp" 
                                                  name="alamat_ktp" 
                                                  rows="3" 
                                                  required>{{ old('alamat_ktp') }}</textarea>
                                        @error('alamat_ktp')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="alamat_domisili" class="form-label">
                                            Alamat Domisili <span class="required">*</span>
                                        </label>
                                        <textarea class="form-control @error('alamat_domisili') is-invalid @enderror" 
                                                  id="alamat_domisili" 
                                                  name="alamat_domisili" 
                                                  rows="3" 
                                                  required>{{ old('alamat_domisili') }}</textarea>
                                        @error('alamat_domisili')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Data Keluarga -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-users me-2"></i>Data Keluarga & Ekonomi
                                    </h5>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="status_pernikahan" class="form-label">
                                            Status Pernikahan <span class="required">*</span>
                                        </label>
                                        <select class="form-select @error('status_pernikahan') is-invalid @enderror" 
                                                id="status_pernikahan" 
                                                name="status_pernikahan" 
                                                required>
                                            <option value="">Pilih Status</option>
                                            <option value="belum_menikah" {{ old('status_pernikahan') == 'belum_menikah' ? 'selected' : '' }}>Belum Menikah</option>
                                            <option value="menikah" {{ old('status_pernikahan') == 'menikah' ? 'selected' : '' }}>Menikah</option>
                                            <option value="cerai_hidup" {{ old('status_pernikahan') == 'cerai_hidup' ? 'selected' : '' }}>Cerai Hidup</option>
                                            <option value="cerai_mati" {{ old('status_pernikahan') == 'cerai_mati' ? 'selected' : '' }}>Cerai Mati</option>
                                        </select>
                                        @error('status_pernikahan')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="jumlah_keluarga" class="form-label">
                                            Jumlah Anggota Keluarga <span class="required">*</span>
                                        </label>
                                        <input type="number" 
                                               class="form-control @error('jumlah_keluarga') is-invalid @enderror" 
                                               id="jumlah_keluarga" 
                                               name="jumlah_keluarga" 
                                               value="{{ old('jumlah_keluarga') }}" 
                                               min="1" 
                                               required>
                                        @error('jumlah_keluarga')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="jumlah_penghasilan" class="form-label">
                                            Jumlah Penghasilan per Bulan (Rp) <span class="required">*</span>
                                        </label>
                                        <input type="number" 
                                               class="form-control @error('jumlah_penghasilan') is-invalid @enderror" 
                                               id="jumlah_penghasilan" 
                                               name="jumlah_penghasilan" 
                                               value="{{ old('jumlah_penghasilan') }}" 
                                               min="0" 
                                               step="1000" 
                                               required>
                                        @error('jumlah_penghasilan')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="mempunyai_rumah" class="form-label">
                                            Mempunyai Rumah <span class="required">*</span>
                                        </label>
                                        <select class="form-select @error('mempunyai_rumah') is-invalid @enderror" 
                                                id="mempunyai_rumah" 
                                                name="mempunyai_rumah" 
                                                required>
                                            <option value="">Pilih</option>
                                            <option value="ya" {{ old('mempunyai_rumah') == 'ya' ? 'selected' : '' }}>Ya</option>
                                            <option value="tidak" {{ old('mempunyai_rumah') == 'tidak' ? 'selected' : '' }}>Tidak</option>
                                        </select>
                                        @error('mempunyai_rumah')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="row mt-5">
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-submit">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        Kirim Pendaftaran
                                    </button>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Pastikan semua data yang Anda masukkan sudah benar
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
