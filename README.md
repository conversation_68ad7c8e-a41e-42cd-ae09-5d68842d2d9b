# Website Pendaftaran Rusun UPTD P3JB

Website pendaftaran penghuni rumah susun (rusun) yang dikelola oleh UPTD P3JB (Unit Pelaksana Teknis Daerah Pengelolaan Prasarana dan <PERSON>, <PERSON><PERSON>, dan <PERSON>awa Barat).

## Fitur Utama

### 🏠 Halaman Publik
- **Landing Page**: Halaman utama dengan informasi rusun dan tombol pendaftaran
- **Tentang Rusun**: Informasi lengkap tentang fasilitas dan spesifikasi rusun
- **Formulir Pendaftaran**: Form pendaftaran online untuk calon penghuni
- **Desain Responsif**: Dapat diakses dengan baik dari desktop dan mobile

### 👨‍💼 Dashboard Admin
- **Login Admin**: Sistem autentikasi untuk administrator
- **Dashboard**: Overview statistik pendaftar dan data terbaru
- **Kelola Pendaftar**: CRUD lengkap untuk data pendaftar
- **Pencarian & Filter**: Fitur pencarian dan filter berdasarkan berbagai kriteria
- **Detail Pendaftar**: Halaman detail lengkap setiap pendaftar

## Teknologi yang Digunakan

- **Backend**: Laravel 11 (PHP)
- **Frontend**: Blade Templates, Bootstrap 5
- **Database**: SQLite (dapat diganti dengan MySQL/PostgreSQL)
- **Icons**: Font Awesome 6
- **Styling**: Custom CSS dengan Bootstrap

## Data yang Dikumpulkan

Formulir pendaftaran mengumpulkan data berikut:
- Nama lengkap
- Nomor Kartu Keluarga (KK)
- Alamat KTP
- Alamat domisili
- Nomor telepon
- Pekerjaan
- Status pernikahan
- Jumlah anggota keluarga
- Jumlah penghasilan per bulan
- Status kepemilikan rumah

## Instalasi

### Prasyarat
- PHP 8.2 atau lebih tinggi
- Composer
- Node.js & NPM (opsional, untuk asset compilation)

### Langkah Instalasi

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd tugasuasdinar
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Setup environment**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Setup database**
   ```bash
   php artisan migrate
   ```

5. **Seed data (opsional)**
   ```bash
   php artisan db:seed --class=AdminSeeder
   php artisan db:seed --class=PendaftarSeeder
   ```

6. **Jalankan server**
   ```bash
   php artisan serve
   ```

Website akan dapat diakses di `http://localhost:8000`

## Login Admin

Setelah menjalankan AdminSeeder, Anda dapat login sebagai admin dengan:
- **Email**: <EMAIL>
- **Password**: password123

## Struktur Proyek

```
├── app/
│   ├── Http/Controllers/
│   │   ├── AdminController.php      # Controller untuk admin
│   │   ├── HomeController.php       # Controller halaman utama
│   │   └── PendaftarController.php  # Controller pendaftaran
│   └── Models/
│       └── Pendaftar.php           # Model data pendaftar
├── database/
│   ├── migrations/
│   │   └── create_pendaftars_table.php
│   └── seeders/
│       ├── AdminSeeder.php
│       └── PendaftarSeeder.php
├── resources/views/
│   ├── layouts/
│   │   ├── app.blade.php           # Layout utama
│   │   └── admin.blade.php         # Layout admin
│   ├── admin/                      # Views admin
│   ├── pendaftar/                  # Views pendaftaran
│   ├── home.blade.php              # Halaman utama
│   └── about.blade.php             # Halaman tentang
└── routes/
    └── web.php                     # Definisi routes
```

## Fitur Admin

### Dashboard
- Statistik total pendaftar
- Data pendaftar terbaru
- Ringkasan berdasarkan status

### Kelola Pendaftar
- Lihat semua data pendaftar dengan pagination
- Pencarian berdasarkan nama, KK, telepon, pekerjaan
- Filter berdasarkan status pernikahan dan kepemilikan rumah
- Detail lengkap setiap pendaftar
- Edit data pendaftar
- Hapus data pendaftar

## Keamanan

- Autentikasi admin menggunakan Laravel Auth
- Validasi input pada semua form
- CSRF protection
- SQL injection protection melalui Eloquent ORM

## Responsif Design

Website telah dioptimalkan untuk berbagai ukuran layar:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (< 768px)

## Kontribusi

Untuk berkontribusi pada proyek ini:
1. Fork repository
2. Buat branch fitur baru
3. Commit perubahan
4. Push ke branch
5. Buat Pull Request

## Lisensi

Proyek ini dibuat untuk keperluan tugas dan pembelajaran.

## Kontak

Untuk pertanyaan atau dukungan, silakan hubungi tim pengembang.
