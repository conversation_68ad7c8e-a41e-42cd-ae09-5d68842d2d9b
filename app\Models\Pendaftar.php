<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Pendaftar extends Model
{
    use HasFactory;
    protected $fillable = [
        'nama_lengkap',
        'no_kk',
        'alamat_ktp',
        'alamat_domisili',
        'no_telepon',
        'pekerjaan',
        'status_pernikahan',
        'jumlah_keluarga',
        'jumlah_penghasilan',
        'mempunyai_rumah'
    ];

    protected $casts = [
        'jumlah_penghasilan' => 'decimal:2',
        'jumlah_keluarga' => 'integer'
    ];
}
