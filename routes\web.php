<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PendaftarController;
use App\Http\Controllers\AdminController;

// Halaman utama
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');

// Pendaftaran publik
Route::get('/daftar', [PendaftarController::class, 'create'])->name('pendaftar.create');
Route::post('/daftar', [PendaftarController::class, 'store'])->name('pendaftar.store');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Admin routes (memerlukan autentikasi)
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/biodata', [AdminController::class, 'biodata'])->name('biodata');
    Route::get('/pendaftar', [AdminController::class, 'pendaftar'])->name('pendaftar');
    Route::get('/pendaftar/{id}', [AdminController::class, 'show'])->name('show');
    Route::get('/pendaftar/{id}/edit', [AdminController::class, 'edit'])->name('edit');
    Route::put('/pendaftar/{id}', [AdminController::class, 'update'])->name('update');
    Route::delete('/pendaftar/{id}', [AdminController::class, 'destroy'])->name('destroy');
});

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
});

require __DIR__.'/auth.php';
