<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Pendaftar>
 */
class PendaftarFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'nama_lengkap' => $this->faker->name(),
            'no_kk' => $this->faker->numerify('################'),
            'alamat_ktp' => $this->faker->address(),
            'alamat_domisili' => $this->faker->address(),
            'no_telepon' => $this->faker->phoneNumber(),
            'pekerjaan' => $this->faker->randomElement([
                '<PERSON>rya<PERSON> Swasta', 'Pegawai Negeri', 'Wiraswasta', 'Guru', 'Teknisi',
                'Do<PERSON>er', 'Perawat', 'Pedagang', 'Supir', 'Buruh'
            ]),
            'status_pernikahan' => $this->faker->randomElement([
                'belum_menikah', 'menikah', 'cerai_hidup', 'cerai_mati'
            ]),
            'jumlah_keluarga' => $this->faker->numberBetween(1, 8),
            'jumlah_penghasilan' => $this->faker->numberBetween(2000000, 10000000),
            'mempunyai_rumah' => $this->faker->randomElement(['ya', 'tidak']),
        ];
    }
}
