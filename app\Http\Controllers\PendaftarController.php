<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Pendaftar;

class PendaftarController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pendaftar.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('pendaftar.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nama_lengkap' => 'required|string|max:255',
            'no_kk' => 'required|string|max:16|unique:pendaftars,no_kk',
            'alamat_ktp' => 'required|string|max:500',
            'alamat_domisili' => 'required|string|max:500',
            'no_telepon' => 'required|string|max:15|regex:/^[0-9+\-\s]+$/',
            'pekerjaan' => 'required|string|max:255',
            'status_pernikahan' => 'required|in:belum_menikah,menikah,cerai_hidup,cerai_mati',
            'jumlah_keluarga' => 'required|integer|min:1|max:20',
            'jumlah_penghasilan' => 'required|numeric|min:0|max:999999999',
            'mempunyai_rumah' => 'required|in:ya,tidak'
        ], [
            'no_kk.unique' => 'Nomor KK sudah terdaftar sebelumnya.',
            'no_telepon.regex' => 'Format nomor telepon tidak valid.',
            'jumlah_keluarga.max' => 'Jumlah keluarga maksimal 20 orang.',
            'jumlah_penghasilan.max' => 'Jumlah penghasilan terlalu besar.'
        ]);

        Pendaftar::create($validated);

        return redirect()->route('pendaftar.create')
            ->with('success', 'Pendaftaran berhasil! Terima kasih telah mendaftar.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
