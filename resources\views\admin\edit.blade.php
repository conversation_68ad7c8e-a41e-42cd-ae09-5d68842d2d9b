@extends('layouts.admin')

@section('title', 'Edit Pendaftar')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.pendaftar') }}">Data Pendaftar</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.show', $pendaftar->id) }}">{{ $pendaftar->nama_lengkap }}</a>
                    </li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit me-2"></i>Edit Data Pendaftar
            </h1>
        </div>
        <div class="btn-group">
            <a href="{{ route('admin.show', $pendaftar->id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Kembali
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-edit me-2"></i>Form Edit Data Pendaftar
                    </h6>
                </div>
                <div class="card-body">
                    @if($errors->any())
                        <div class="alert alert-danger mb-4">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Terdapat kesalahan:</h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.update', $pendaftar->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <!-- Data Pribadi -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>Data Pribadi
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="nama_lengkap" class="form-label">
                                        Nama Lengkap <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control @error('nama_lengkap') is-invalid @enderror" 
                                           id="nama_lengkap" 
                                           name="nama_lengkap" 
                                           value="{{ old('nama_lengkap', $pendaftar->nama_lengkap) }}" 
                                           required>
                                    @error('nama_lengkap')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="no_kk" class="form-label">
                                        Nomor Kartu Keluarga <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control @error('no_kk') is-invalid @enderror" 
                                           id="no_kk" 
                                           name="no_kk" 
                                           value="{{ old('no_kk', $pendaftar->no_kk) }}" 
                                           required>
                                    @error('no_kk')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="no_telepon" class="form-label">
                                        Nomor Telepon <span class="text-danger">*</span>
                                    </label>
                                    <input type="tel" 
                                           class="form-control @error('no_telepon') is-invalid @enderror" 
                                           id="no_telepon" 
                                           name="no_telepon" 
                                           value="{{ old('no_telepon', $pendaftar->no_telepon) }}" 
                                           required>
                                    @error('no_telepon')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="pekerjaan" class="form-label">
                                        Pekerjaan <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control @error('pekerjaan') is-invalid @enderror" 
                                           id="pekerjaan" 
                                           name="pekerjaan" 
                                           value="{{ old('pekerjaan', $pendaftar->pekerjaan) }}" 
                                           required>
                                    @error('pekerjaan')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <!-- Alamat -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>Alamat
                                </h5>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="alamat_ktp" class="form-label">
                                        Alamat KTP <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control @error('alamat_ktp') is-invalid @enderror" 
                                              id="alamat_ktp" 
                                              name="alamat_ktp" 
                                              rows="3" 
                                              required>{{ old('alamat_ktp', $pendaftar->alamat_ktp) }}</textarea>
                                    @error('alamat_ktp')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="alamat_domisili" class="form-label">
                                        Alamat Domisili <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control @error('alamat_domisili') is-invalid @enderror" 
                                              id="alamat_domisili" 
                                              name="alamat_domisili" 
                                              rows="3" 
                                              required>{{ old('alamat_domisili', $pendaftar->alamat_domisili) }}</textarea>
                                    @error('alamat_domisili')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <!-- Data Keluarga -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-users me-2"></i>Data Keluarga & Ekonomi
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="status_pernikahan" class="form-label">
                                        Status Pernikahan <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('status_pernikahan') is-invalid @enderror" 
                                            id="status_pernikahan" 
                                            name="status_pernikahan" 
                                            required>
                                        <option value="">Pilih Status</option>
                                        <option value="belum_menikah" {{ old('status_pernikahan', $pendaftar->status_pernikahan) == 'belum_menikah' ? 'selected' : '' }}>
                                            Belum Menikah
                                        </option>
                                        <option value="menikah" {{ old('status_pernikahan', $pendaftar->status_pernikahan) == 'menikah' ? 'selected' : '' }}>
                                            Menikah
                                        </option>
                                        <option value="cerai_hidup" {{ old('status_pernikahan', $pendaftar->status_pernikahan) == 'cerai_hidup' ? 'selected' : '' }}>
                                            Cerai Hidup
                                        </option>
                                        <option value="cerai_mati" {{ old('status_pernikahan', $pendaftar->status_pernikahan) == 'cerai_mati' ? 'selected' : '' }}>
                                            Cerai Mati
                                        </option>
                                    </select>
                                    @error('status_pernikahan')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="jumlah_keluarga" class="form-label">
                                        Jumlah Anggota Keluarga <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" 
                                           class="form-control @error('jumlah_keluarga') is-invalid @enderror" 
                                           id="jumlah_keluarga" 
                                           name="jumlah_keluarga" 
                                           value="{{ old('jumlah_keluarga', $pendaftar->jumlah_keluarga) }}" 
                                           min="1" 
                                           required>
                                    @error('jumlah_keluarga')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="jumlah_penghasilan" class="form-label">
                                        Jumlah Penghasilan per Bulan (Rp) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" 
                                           class="form-control @error('jumlah_penghasilan') is-invalid @enderror" 
                                           id="jumlah_penghasilan" 
                                           name="jumlah_penghasilan" 
                                           value="{{ old('jumlah_penghasilan', $pendaftar->jumlah_penghasilan) }}" 
                                           min="0" 
                                           step="1000" 
                                           required>
                                    @error('jumlah_penghasilan')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="mempunyai_rumah" class="form-label">
                                        Mempunyai Rumah <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('mempunyai_rumah') is-invalid @enderror" 
                                            id="mempunyai_rumah" 
                                            name="mempunyai_rumah" 
                                            required>
                                        <option value="">Pilih</option>
                                        <option value="ya" {{ old('mempunyai_rumah', $pendaftar->mempunyai_rumah) == 'ya' ? 'selected' : '' }}>
                                            Ya
                                        </option>
                                        <option value="tidak" {{ old('mempunyai_rumah', $pendaftar->mempunyai_rumah) == 'tidak' ? 'selected' : '' }}>
                                            Tidak
                                        </option>
                                    </select>
                                    @error('mempunyai_rumah')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="row mt-5">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.show', $pendaftar->id) }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>Kembali
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-undo me-1"></i>Reset
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>Simpan Perubahan
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .form-control, .form-select {
        border: 2px solid #e3e6f0;
        border-radius: 8px;
        padding: 12px 16px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
    }
    
    .form-label {
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 0.5rem;
    }
    
    .text-danger {
        color: #e74a3b !important;
    }
</style>
@endpush
