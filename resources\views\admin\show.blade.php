@extends('layouts.admin')

@section('title', 'Detail Pendaftar')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.pendaftar') }}">Data Pendaftar</a>
                    </li>
                    <li class="breadcrumb-item active">Detail</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user me-2"></i>Detail Pendaftar
            </h1>
        </div>
        <div class="btn-group">
            <a href="{{ route('admin.edit', $pendaftar->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i>Edit
            </a>
            <button type="button" 
                    class="btn btn-danger" 
                    onclick="confirmDelete({{ $pendaftar->id }}, '{{ $pendaftar->nama_lengkap }}')">
                <i class="fas fa-trash me-1"></i>Hapus
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Main Info Card -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-id-card me-2"></i>Informasi Pribadi
                    </h6>
                    <div class="text-muted small">
                        ID: {{ $pendaftar->id }}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Nama Lengkap</label>
                            <div class="fw-bold">{{ $pendaftar->nama_lengkap }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Nomor Kartu Keluarga</label>
                            <div class="fw-bold">{{ $pendaftar->no_kk }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Nomor Telepon</label>
                            <div class="fw-bold">
                                <a href="tel:{{ $pendaftar->no_telepon }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>{{ $pendaftar->no_telepon }}
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Pekerjaan</label>
                            <div class="fw-bold">{{ $pendaftar->pekerjaan }}</div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Alamat KTP</label>
                            <div class="fw-bold">{{ $pendaftar->alamat_ktp }}</div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Alamat Domisili</label>
                            <div class="fw-bold">{{ $pendaftar->alamat_domisili }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family & Economic Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>Informasi Keluarga & Ekonomi
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Status Pernikahan</label>
                            <div>
                                <span class="badge bg-{{ $pendaftar->status_pernikahan == 'menikah' ? 'success' : 'secondary' }} fs-6">
                                    {{ ucwords(str_replace('_', ' ', $pendaftar->status_pernikahan)) }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Jumlah Anggota Keluarga</label>
                            <div class="fw-bold">
                                <i class="fas fa-users me-1"></i>{{ $pendaftar->jumlah_keluarga }} orang
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Penghasilan per Bulan</label>
                            <div class="fw-bold text-success">
                                <i class="fas fa-money-bill-wave me-1"></i>
                                Rp {{ number_format($pendaftar->jumlah_penghasilan, 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Kepemilikan Rumah</label>
                            <div>
                                <span class="badge bg-{{ $pendaftar->mempunyai_rumah == 'ya' ? 'success' : 'warning' }} fs-6">
                                    <i class="fas fa-home me-1"></i>
                                    {{ $pendaftar->mempunyai_rumah == 'ya' ? 'Sudah Punya Rumah' : 'Belum Punya Rumah' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Registration Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar me-2"></i>Informasi Pendaftaran
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Tanggal Pendaftaran</label>
                        <div class="fw-bold">
                            <i class="fas fa-calendar-alt me-1"></i>
                            {{ $pendaftar->created_at->format('d F Y') }}
                        </div>
                        <small class="text-muted">{{ $pendaftar->created_at->format('H:i') }} WIB</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Terakhir Diperbarui</label>
                        <div class="fw-bold">
                            <i class="fas fa-clock me-1"></i>
                            {{ $pendaftar->updated_at->format('d F Y') }}
                        </div>
                        <small class="text-muted">{{ $pendaftar->updated_at->format('H:i') }} WIB</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Lama Terdaftar</label>
                        <div class="fw-bold">
                            <i class="fas fa-hourglass-half me-1"></i>
                            {{ $pendaftar->created_at->diffForHumans() }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>Aksi Cepat
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.edit', $pendaftar->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Edit Data
                        </a>
                        <a href="tel:{{ $pendaftar->no_telepon }}" class="btn btn-success">
                            <i class="fas fa-phone me-2"></i>Hubungi
                        </a>
                        <button type="button" class="btn btn-info" onclick="printData()">
                            <i class="fas fa-print me-2"></i>Cetak Data
                        </button>
                        <hr>
                        <button type="button" 
                                class="btn btn-danger" 
                                onclick="confirmDelete({{ $pendaftar->id }}, '{{ $pendaftar->nama_lengkap }}')">
                            <i class="fas fa-trash me-2"></i>Hapus Data
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Statistik
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Total Pendaftar</span>
                            <span class="fw-bold">{{ \App\Models\Pendaftar::count() }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Status: {{ ucwords(str_replace('_', ' ', $pendaftar->status_pernikahan)) }}</span>
                            <span class="fw-bold">
                                {{ \App\Models\Pendaftar::where('status_pernikahan', $pendaftar->status_pernikahan)->count() }}
                            </span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Pekerjaan: {{ $pendaftar->pekerjaan }}</span>
                            <span class="fw-bold">
                                {{ \App\Models\Pendaftar::where('pekerjaan', $pendaftar->pekerjaan)->count() }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Konfirmasi Hapus
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus data pendaftar <strong id="deleteName"></strong>?</p>
                <p class="text-muted small">Tindakan ini tidak dapat dibatalkan.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Hapus
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function confirmDelete(id, name) {
        document.getElementById('deleteName').textContent = name;
        document.getElementById('deleteForm').action = '/admin/pendaftar/' + id;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function printData() {
        window.print();
    }
</script>
@endpush

@push('styles')
<style>
    @media print {
        .btn, .card-header, nav, .sidebar {
            display: none !important;
        }
        
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        
        .main-content {
            margin-left: 0 !important;
        }
    }
</style>
@endpush
