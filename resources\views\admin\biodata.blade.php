@extends('layouts.admin')

@section('title', 'Biodata Pendaftar')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-address-card me-2"></i>Biodata Pendaftar Rusun
        </h1>
        <div class="text-muted">
            Total: {{ $pendaftars->total() }} pendaftar terdaftar
        </div>
    </div>

    <!-- Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>Filter & Pencarian Biodata
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.biodata') }}">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Pencarian</label>
                        <input type="text" 
                               class="form-control" 
                               id="search" 
                               name="search" 
                               value="{{ request('search') }}" 
                               placeholder="Nama, No KK, Telepon, Pekerjaan...">
                    </div>
                    <div class="col-md-3">
                        <label for="status_pernikahan" class="form-label">Status Pernikahan</label>
                        <select class="form-select" id="status_pernikahan" name="status_pernikahan">
                            <option value="">Semua Status</option>
                            <option value="belum_menikah" {{ request('status_pernikahan') == 'belum_menikah' ? 'selected' : '' }}>
                                Belum Menikah
                            </option>
                            <option value="menikah" {{ request('status_pernikahan') == 'menikah' ? 'selected' : '' }}>
                                Menikah
                            </option>
                            <option value="cerai_hidup" {{ request('status_pernikahan') == 'cerai_hidup' ? 'selected' : '' }}>
                                Cerai Hidup
                            </option>
                            <option value="cerai_mati" {{ request('status_pernikahan') == 'cerai_mati' ? 'selected' : '' }}>
                                Cerai Mati
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="mempunyai_rumah" class="form-label">Kepemilikan Rumah</label>
                        <select class="form-select" id="mempunyai_rumah" name="mempunyai_rumah">
                            <option value="">Semua</option>
                            <option value="ya" {{ request('mempunyai_rumah') == 'ya' ? 'selected' : '' }}>
                                Sudah Punya Rumah
                            </option>
                            <option value="tidak" {{ request('mempunyai_rumah') == 'tidak' ? 'selected' : '' }}>
                                Belum Punya Rumah
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="{{ route('admin.biodata') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Biodata Cards -->
    <div class="row">
        @if($pendaftars->count() > 0)
            @foreach($pendaftars as $index => $pendaftar)
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card border-left-primary h-100 biodata-card">
                        <div class="card-body">
                            <!-- Header dengan foto dan nama -->
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-user text-white fa-lg"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5 class="font-weight-bold text-primary mb-1">{{ $pendaftar->nama_lengkap }}</h5>
                                            <small class="text-muted">ID: {{ $pendaftar->id }} | Terdaftar: {{ $pendaftar->created_at->format('d/m/Y') }}</small>
                                        </div>
                                        <span class="badge bg-primary">{{ $pendaftars->firstItem() + $index }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Biodata lengkap -->
                            <div class="biodata-details">
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-id-card me-1"></i>No. KK:</small>
                                    </div>
                                    <div class="col-8">
                                        <small class="font-weight-bold">{{ $pendaftar->no_kk }}</small>
                                    </div>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-phone me-1"></i>Telepon:</small>
                                    </div>
                                    <div class="col-8">
                                        <small class="font-weight-bold">
                                            <a href="tel:{{ $pendaftar->no_telepon }}" class="text-decoration-none">
                                                {{ $pendaftar->no_telepon }}
                                            </a>
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-briefcase me-1"></i>Pekerjaan:</small>
                                    </div>
                                    <div class="col-8">
                                        <small class="font-weight-bold">{{ $pendaftar->pekerjaan }}</small>
                                    </div>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-heart me-1"></i>Status:</small>
                                    </div>
                                    <div class="col-8">
                                        <span class="badge bg-{{ $pendaftar->status_pernikahan == 'menikah' ? 'success' : 'secondary' }} badge-sm">
                                            {{ ucwords(str_replace('_', ' ', $pendaftar->status_pernikahan)) }}
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-users me-1"></i>Keluarga:</small>
                                    </div>
                                    <div class="col-8">
                                        <small class="font-weight-bold">{{ $pendaftar->jumlah_keluarga }} orang</small>
                                    </div>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-money-bill-wave me-1"></i>Penghasilan:</small>
                                    </div>
                                    <div class="col-8">
                                        <small class="font-weight-bold text-success">
                                            Rp {{ number_format($pendaftar->jumlah_penghasilan, 0, ',', '.') }}
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-home me-1"></i>Rumah:</small>
                                    </div>
                                    <div class="col-8">
                                        <span class="badge bg-{{ $pendaftar->mempunyai_rumah == 'ya' ? 'success' : 'warning' }} badge-sm">
                                            {{ $pendaftar->mempunyai_rumah == 'ya' ? 'Sudah Punya' : 'Belum Punya' }}
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-map-marker-alt me-1"></i>Alamat KTP:</small>
                                    </div>
                                    <div class="col-8">
                                        <small class="font-weight-bold">
                                            {{ Str::limit($pendaftar->alamat_ktp, 60) }}
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-4">
                                        <small class="text-muted"><i class="fas fa-location-arrow me-1"></i>Domisili:</small>
                                    </div>
                                    <div class="col-8">
                                        <small class="font-weight-bold">
                                            {{ Str::limit($pendaftar->alamat_domisili, 60) }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Action buttons -->
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.show', $pendaftar->id) }}" 
                                       class="btn btn-outline-info btn-sm" 
                                       title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.edit', $pendaftar->id) }}" 
                                       class="btn btn-outline-warning btn-sm" 
                                       title="Edit Data">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="tel:{{ $pendaftar->no_telepon }}" 
                                       class="btn btn-outline-success btn-sm" 
                                       title="Hubungi">
                                        <i class="fas fa-phone"></i>
                                    </a>
                                </div>
                                <small class="text-muted">
                                    {{ $pendaftar->created_at->diffForHumans() }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                        <h5 class="text-muted">Tidak Ada Data Biodata</h5>
                        <p class="text-muted">
                            @if(request()->hasAny(['search', 'status_pernikahan', 'mempunyai_rumah']))
                                Tidak ada biodata yang sesuai dengan filter yang dipilih.
                            @else
                                Belum ada pendaftar yang terdaftar.
                            @endif
                        </p>
                        @if(request()->hasAny(['search', 'status_pernikahan', 'mempunyai_rumah']))
                            <a href="{{ route('admin.biodata') }}" class="btn btn-primary">
                                <i class="fas fa-times me-1"></i>Reset Filter
                            </a>
                        @else
                            <a href="{{ route('pendaftar.create') }}" class="btn btn-primary" target="_blank">
                                <i class="fas fa-plus me-1"></i>Buka Form Pendaftaran
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Pagination -->
    @if($pendaftars->hasPages())
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div class="text-muted">
                Menampilkan {{ $pendaftars->firstItem() }} - {{ $pendaftars->lastItem() }} 
                dari {{ $pendaftars->total() }} biodata
            </div>
            <div>
                {{ $pendaftars->appends(request()->query())->links() }}
            </div>
        </div>
    @endif
</div>
@endsection

@push('styles')
<style>
    .biodata-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }
    
    .biodata-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .biodata-details {
        font-size: 0.85rem;
    }
    
    .biodata-details .row {
        margin-bottom: 0.25rem;
    }
    
    .badge-sm {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    .avatar {
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    
    .btn-group .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
</style>
@endpush
